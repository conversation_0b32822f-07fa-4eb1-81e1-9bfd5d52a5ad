

## 天气信息接口

api：127.0.0.1:8080/api/gzt/weather/query

获取七天 天气信息

```
{
    "msg": "success",
    "code": 0,
    "data": {
        "code": "200",
        "updateTime": "2025-09-27T08:42+08:00",
        "fxLink": "https://www.qweather.com/weather/hebei-101031600.html",
        "daily": [
            {
                "fxDate": "2025-09-27",
                "sunrise": "06:04",
                "sunset": "18:02",
                "moonrise": "11:20",
                "moonset": "20:32",
                "moonPhase": "蛾眉月",
                "moonPhaseIcon": "801",
                "tempMax": "25",
                "tempMin": "18",
                "iconDay": "305",
                "textDay": "小雨",
                "iconNight": "104",
                "textNight": "阴",
                "wind360Day": "225",
                "windDirDay": "西南风",
                "windScaleDay": "1-3",
                "windSpeedDay": "3",
                "wind360Night": "270",
                "windDirNight": "西风",
                "windScaleNight": "1-3",
                "windSpeedNight": "3",
                "humidity": "88",
                "precip": "0.0",
                "pressure": "1013",
                "vis": "25",
                "cloud": "25",
                "uvIndex": "5"
            },
            {
                "fxDate": "2025-09-28",
                "sunrise": "06:05",
                "sunset": "18:01",
                "moonrise": "12:19",
                "moonset": "21:16",
                "moonPhase": "蛾眉月",
                "moonPhaseIcon": "801",
                "tempMax": "27",
                "tempMin": "18",
                "iconDay": "100",
                "textDay": "晴",
                "iconNight": "150",
                "textNight": "晴",
                "wind360Day": "270",
                "windDirDay": "西风",
                "windScaleDay": "1-3",
                "windSpeedDay": "16",
                "wind360Night": "270",
                "windDirNight": "西风",
                "windScaleNight": "1-3",
                "windSpeedNight": "3",
                "humidity": "71",
                "precip": "0.0",
                "pressure": "1014",
                "vis": "25",
                "cloud": "1",
                "uvIndex": "5"
            },
            {
                "fxDate": "2025-09-29",
                "sunrise": "06:06",
                "sunset": "17:59",
                "moonrise": "13:14",
                "moonset": "22:08",
                "moonPhase": "蛾眉月",
                "moonPhaseIcon": "801",
                "tempMax": "29",
                "tempMin": "19",
                "iconDay": "100",
                "textDay": "晴",
                "iconNight": "151",
                "textNight": "多云",
                "wind360Day": "225",
                "windDirDay": "西南风",
                "windScaleDay": "1-3",
                "windSpeedDay": "3",
                "wind360Night": "225",
                "windDirNight": "西南风",
                "windScaleNight": "1-3",
                "windSpeedNight": "3",
                "humidity": "67",
                "precip": "0.0",
                "pressure": "1010",
                "vis": "25",
                "cloud": "1",
                "uvIndex": "4"
            },
            {
                "fxDate": "2025-09-30",
                "sunrise": "06:07",
                "sunset": "17:58",
                "moonrise": "14:02",
                "moonset": "23:08",
                "moonPhase": "上弦月",
                "moonPhaseIcon": "802",
                "tempMax": "28",
                "tempMin": "18",
                "iconDay": "100",
                "textDay": "晴",
                "iconNight": "150",
                "textNight": "晴",
                "wind360Day": "225",
                "windDirDay": "西南风",
                "windScaleDay": "1-3",
                "windSpeedDay": "3",
                "wind360Night": "225",
                "windDirNight": "西南风",
                "windScaleNight": "1-3",
                "windSpeedNight": "3",
                "humidity": "69",
                "precip": "0.0",
                "pressure": "1009",
                "vis": "25",
                "cloud": "1",
                "uvIndex": "5"
            },
            {
                "fxDate": "2025-10-01",
                "sunrise": "06:07",
                "sunset": "17:56",
                "moonrise": "14:45",
                "moonset": "",
                "moonPhase": "盈凸月",
                "moonPhaseIcon": "803",
                "tempMax": "29",
                "tempMin": "19",
                "iconDay": "101",
                "textDay": "多云",
                "iconNight": "151",
                "textNight": "多云",
                "wind360Day": "225",
                "windDirDay": "西南风",
                "windScaleDay": "1-3",
                "windSpeedDay": "3",
                "wind360Night": "225",
                "windDirNight": "西南风",
                "windScaleNight": "1-3",
                "windSpeedNight": "3",
                "humidity": "69",
                "precip": "0.0",
                "pressure": "1015",
                "vis": "25",
                "cloud": "1",
                "uvIndex": "2"
            },
            {
                "fxDate": "2025-10-02",
                "sunrise": "06:08",
                "sunset": "17:54",
                "moonrise": "15:20",
                "moonset": "00:13",
                "moonPhase": "盈凸月",
                "moonPhaseIcon": "803",
                "tempMax": "29",
                "tempMin": "18",
                "iconDay": "100",
                "textDay": "晴",
                "iconNight": "151",
                "textNight": "多云",
                "wind360Day": "225",
                "windDirDay": "西南风",
                "windScaleDay": "1-3",
                "windSpeedDay": "3",
                "wind360Night": "225",
                "windDirNight": "西南风",
                "windScaleNight": "1-3",
                "windSpeedNight": "3",
                "humidity": "82",
                "precip": "0.0",
                "pressure": "1016",
                "vis": "25",
                "cloud": "1",
                "uvIndex": "5"
            },
            {
                "fxDate": "2025-10-03",
                "sunrise": "06:09",
                "sunset": "17:53",
                "moonrise": "15:51",
                "moonset": "01:21",
                "moonPhase": "盈凸月",
                "moonPhaseIcon": "803",
                "tempMax": "24",
                "tempMin": "19",
                "iconDay": "101",
                "textDay": "多云",
                "iconNight": "151",
                "textNight": "多云",
                "wind360Day": "0",
                "windDirDay": "北风",
                "windScaleDay": "1-3",
                "windSpeedDay": "3",
                "wind360Night": "0",
                "windDirNight": "北风",
                "windScaleNight": "1-3",
                "windSpeedNight": "3",
                "humidity": "78",
                "precip": "0.0",
                "pressure": "1014",
                "vis": "25",
                "cloud": "2",
                "uvIndex": "4"
            }
        ],
        "refer": {
            "sources": [
                "QWeather"
            ],
            "license": [
                "QWeather Developers License"
            ]
        }
    }
}
```



```
code 请参考状态码
updateTime 当前API的最近更新时间
fxLink 当前数据的响应式页面，便于嵌入网站或应用
daily.fxDate 预报日期
daily.sunrise 日出时间，在高纬度地区可能为空
daily.sunset 日落时间，在高纬度地区可能为空
daily.moonrise 当天月升时间，可能为空
daily.moonset 当天月落时间，可能为空
daily.moonPhase 月相名称
daily.moonPhaseIcon 月相图标代码，另请参考天气图标项目
daily.tempMax 预报当天最高温度
daily.tempMin 预报当天最低温度
daily.iconDay 预报白天天气状况的图标代码，另请参考天气图标项目
daily.textDay 预报白天天气状况文字描述，包括阴晴雨雪等天气状态的描述
daily.iconNight 预报夜间天气状况的图标代码，另请参考天气图标项目
daily.textNight 预报晚间天气状况文字描述，包括阴晴雨雪等天气状态的描述
daily.wind360Day 预报白天风向360角度
daily.windDirDay 预报白天风向
daily.windScaleDay 预报白天风力等级
daily.windSpeedDay 预报白天风速，公里/小时
daily.wind360Night 预报夜间风向360角度
daily.windDirNight 预报夜间当天风向
daily.windScaleNight 预报夜间风力等级
daily.windSpeedNight 预报夜间风速，公里/小时
daily.precip 预报当天总降水量，默认单位：毫米
daily.uvIndex 紫外线强度指数
daily.humidity 相对湿度，百分比数值
daily.pressure 大气压强，默认单位：百帕
daily.vis 能见度，默认单位：公里
daily.cloud 云量，百分比数值。可能为空
refer.sources 原始数据来源，或数据源说明，可能为空
refer.license 数据许可或版权声明，可能为空
```





## 视频接口

api：http://localhost/dev-api/api/video/liveCamera/page/0001?page=1&limit=10&t=1758957190167



获取视频信息接口





```
{
    "msg": "success",
    "code": 0,
    "page": {
        "totalCount": 2,
        "pageSize": 10,
        "totalPage": 1,
        "currPage": 1,
        "list": [
            {
                "id": "01JVTTHY6N5SCVZS33824KTA2D",
                "createUserId": "16e5737c8c0b4294a34664f209743ca4",
                "updateUserId": "16e5737c8c0b4294a34664f209743ca4",
                "createTime": "2025-05-22 09:43:33",
                "updateTime": "2025-05-23 14:46:28",
                "deptId": "01JV989V4EECKWY8QX0A14E32D",
                "deptCode": "0001-0008-0003-0002",
                "name": "河道",
                "longitude": null,
                "latitude": null,
                "platform": "xiaodu",
                "platformSn": "",
                "channel": "",
                "appKey": "",
                "appSecret": "",
                "appUrl": "http://116.204.81.43:18080/#/play/wasm/ws%3A%2F%2F116.204.81.43%3A88%2Frtp%2F13018401411327000019_13018401411327000019.live.flv%3ForiginTypeStr%3Drtp_push"
            },
            {
                "id": "01JNT41CHNA8F6S1BCK5TRCX0T",
                "createUserId": "16e5737c8c0b4294a34664f209743ca4",
                "updateUserId": "16e5737c8c0b4294a34664f209743ca4",
                "createTime": "2025-03-08 13:35:51",
                "updateTime": "2025-03-08 16:29:56",
                "deptId": "01JHF43MTK5ZXG68AFNYEW373S",
                "deptCode": "0001-0010-0003",
                "name": "测试乐橙",
                "longitude": null,
                "latitude": null,
                "platform": "leChengYun",
                "platformSn": "8B013B1PAZ65CDF",
                "channel": "0",
                "appKey": "lc2a02529b42904fec",
                "appSecret": "4a418595669b418f8d799a7608953c",
                "appUrl": ""
            }
        ]
    }
}
```





```
package com.ybkj.smm.modules.video.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 摄像头表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-14 17:05:23
 */
@TableName("smm_video_live_camera")
public class LiveCamera implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * id 主键
	 */
	@TableId
	@Size(max = 32,message = "id 主键最大允许长度为32")
	private String id;

	/**
	 * 创建人id
	 */
	@Size(max = 32,message = "创建人id最大允许长度为32")
	private String createUserId;

	/**
	 * 修改人id
	 */
	@Size(max = 32,message = "修改人id最大允许长度为32")
	private String updateUserId;

	/**
	 * 创建时间
	 */
	@Size(max = 20,message = "创建时间最大允许长度为20")
	private String createTime;

	/**
	 * 修改时间
	 */
	@Size(max = 20,message = "修改时间最大允许长度为20")
	private String updateTime;

	/**
	 * 项目ID
	 */
	@Size(max = 32,message = "项目ID最大允许长度为32")
	private String deptId;

	/**
	 * 项目对应部门CODE
	 */
	@Size(max = 128,message = "项目对应部门CODE最大允许长度为128")
	private String deptCode;

	/**
	 * 名称
	 */
	@Size(max = 128,message = "名称最大允许长度为128")
	private String name;

	/**
	 * 经度
	 */
	@Digits(integer = 4,fraction = 6,message = "经度的值超出了允许范围(只允许在4位整数和6位小数范围内)")
	private BigDecimal longitude;

	/**
	 * 纬度
	 */
	@Digits(integer = 4,fraction = 6,message = "纬度的值超出了允许范围(只允许在4位整数和6位小数范围内)")
	private BigDecimal latitude;

	/**
	 * 接入平台;字典值： yingShi-萤石云
	 */
	@Size(max = 64,message = "接入平台;字典值： yingShi-萤石云最大允许长度为64")
	private String platform;

	/**
	 * 接入平台序列号
	 */
	@Size(max = 128,message = "接入平台序列号最大允许长度为128")
	private String platformSn;

	/**
	 * 设备通道号
	 */
	@Size(max = 20,message = "设备通道号最大允许长度为20")
	private String channel;

	/**
	 * 视频平台appKey
	 */
	@Size(max = 128,message = "视频平台appKey最大允许长度为128")
	private String appKey;

	/**
	 * 视频平台秘钥
	 */
	@Size(max = 128,message = "视频平台秘钥最大允许长度为128")
	private String appSecret;

	/**
	 * 小度云平台视频播放URL
	 */
	@Size(max = 255,message = "小度云平台视频播放URL")
	private String appUrl;


	/**
	 * 设置：id 主键
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * 获取：id 主键
	 */
	public String getId() {
		return id;
	}

	/**
	 * 设置：创建人id
	 */
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	/**
	 * 获取：创建人id
	 */
	public String getCreateUserId() {
		return createUserId;
	}

	/**
	 * 设置：修改人id
	 */
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}

	/**
	 * 获取：修改人id
	 */
	public String getUpdateUserId() {
		return updateUserId;
	}

	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	/**
	 * 获取：创建时间
	 */
	public String getCreateTime() {
		return createTime;
	}

	/**
	 * 设置：修改时间
	 */
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	/**
	 * 获取：修改时间
	 */
	public String getUpdateTime() {
		return updateTime;
	}

	/**
	 * 设置：项目ID
	 */
	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}

	/**
	 * 获取：项目ID
	 */
	public String getDeptId() {
		return deptId;
	}

	/**
	 * 设置：项目对应部门CODE
	 */
	public void setDeptCode(String deptCode) {
		this.deptCode = deptCode;
	}

	/**
	 * 获取：项目对应部门CODE
	 */
	public String getDeptCode() {
		return deptCode;
	}

	/**
	 * 设置：名称
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * 获取：名称
	 */
	public String getName() {
		return name;
	}

	/**
	 * 设置：经度
	 */
	public void setLongitude(BigDecimal longitude) {
		this.longitude = longitude;
	}

	/**
	 * 获取：经度
	 */
	public BigDecimal getLongitude() {
		return longitude;
	}

	/**
	 * 设置：纬度
	 */
	public void setLatitude(BigDecimal latitude) {
		this.latitude = latitude;
	}

	/**
	 * 获取：纬度
	 */
	public BigDecimal getLatitude() {
		return latitude;
	}

	/**
	 * 设置：接入平台;字典值： yingShi-萤石云
	 */
	public void setPlatform(String platform) {
		this.platform = platform;
	}

	/**
	 * 获取：接入平台;字典值： yingShi-萤石云
	 */
	public String getPlatform() {
		return platform;
	}

	/**
	 * 设置：接入平台序列号
	 */
	public void setPlatformSn(String platformSn) {
		this.platformSn = platformSn;
	}

	/**
	 * 获取：接入平台序列号
	 */
	public String getPlatformSn() {
		return platformSn;
	}

	/**
	 * 设置：设备通道号
	 */
	public void setChannel(String channel) {
		this.channel = channel;
	}

	/**
	 * 获取：设备通道号
	 */
	public String getChannel() {
		return channel;
	}

	/**
	 * 设置：视频平台appKey
	 */
	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}

	/**
	 * 获取：视频平台appKey
	 */
	public String getAppKey() {
		return appKey;
	}

	/**
	 * 设置：视频平台秘钥
	 */
	public void setAppSecret(String appSecret) {
		this.appSecret = appSecret;
	}

	/**
	 * 获取：视频平台秘钥
	 */
	public String getAppSecret() {
		return appSecret;
	}

	public void setAppUrl(String appUrl) {
		this.appUrl = appUrl;
	}
	public String getAppUrl() {
		return appUrl;
	}
}

```





































